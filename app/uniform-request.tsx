import { FormInput, FormSelect, FormSubmitButton } from "@/components/forms";
import { Colors } from "@/constants/Colors";
import { zodResolver } from "@hookform/resolvers/zod";
import { StatusBar } from "expo-status-bar";
import React, { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  View,
} from "react-native";
import { z } from "zod";

import {
  CreateInventoryRequestInput,
  useCreateInventoryRequestMutation,
  useGetInventoryQuery,
} from "@/generated/graphql";
import { createEnumFromArray, enumToOptions } from "@/lib/utils";

interface InventoryItem {
  item: string;
  quantity: number;
  sku: string;
  costPrice: number;
  sellingPrice: number;
}

interface Attribute {
  attibuteName: string;
  attributeValues: string[];
}

interface InventoryProduct {
  _id: string;
  id: string;
  createdAt: string;
  updatedAt: string;
  item: string;
  description: string;
  type: string;
  items: InventoryItem[];
  attributes: Attribute[];
}

interface DropdownOption {
  label: string;
  value: string;
}

export default function UniformRequestScreen() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { data: inventoryData, isLoading } = useGetInventoryQuery();
  const { mutateAsync: requestFromInventory } =
    useCreateInventoryRequestMutation();

  // Create gear type options from inventory items
  const gearTypeEnum = createEnumFromArray(
    inventoryData?.inventory?.map((item) => item.item) || []
  );
  const gearTypeOptions = enumToOptions(gearTypeEnum);

  // Schema for uniform request form
  const uniformRequestSchema = z.object({
    
  });

  useCreateInventoryRequestMutation

  type UniformRequestFormData = CreateInventoryRequestInput;

  // Default values for uniform request form
  const defaultUniformRequestValues: UniformRequestFormData = {
    gearType: "",
  };

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { isValid },
  } = useForm<UniformRequestFormData>({
    defaultValues: defaultUniformRequestValues,
    resolver: zodResolver(uniformRequestSchema),
    mode: "onChange",
  });

  // Watch the selected gear type to update size options
  const selectedGearType = watch("gearType");

  // Helper function to get attribute by name
  const getAttributeByName = (selectedItem: any, attributeName: string) => {
    return selectedItem?.attributes?.find(
      (attr: any) =>
        attr.attibuteName.toLowerCase() === attributeName.toLowerCase()
    );
  };

  // Get size options for the selected gear type
  const gearSizeOptions = useMemo(() => {
    if (!selectedGearType) return [];

    const selectedItem = inventoryData?.inventory?.find(
      (item) => item.item === selectedGearType
    );

    const sizeAttribute = getAttributeByName(selectedItem, "size");
    if (!sizeAttribute?.attributeValues) return [];

    return sizeAttribute.attributeValues.map((size: string) => ({
      label: size,
      value: size,
    }));
  }, [selectedGearType, inventoryData]);

  // Get color options for the selected gear type
  const gearColorOptions = useMemo(() => {
    if (!selectedGearType) return [];

    const selectedItem = inventoryData?.inventory?.find(
      (item) => item.item === selectedGearType
    );

    const colorAttribute = getAttributeByName(selectedItem, "color");
    if (!colorAttribute?.attributeValues) return [];

    return colorAttribute.attributeValues.map((color: string) => ({
      label: color,
      value: color,
    }));
  }, [selectedGearType, inventoryData]);

  // Reset size and color when gear type changes
  useEffect(() => {
    if (selectedGearType) {
    }
  }, [selectedGearType, setValue]);

  // Handle form submission
  const onSubmit = (data: UniformRequestFormData) => {};

  return (
    <View style={styles.flex}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={100}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.flex}
          showsVerticalScrollIndicator={false}
        >
          {/* Gear Type Dropdown */}
          <FormSelect
            name="gearType"
            control={control}
            label="Select Gear"
            placeholder="Select Value"
            options={gearTypeOptions}
          />

          {/* Size Dropdown */}
          {/* {selectedGearType && gearSizeOptions.length > 0 && (
            <FormSelect
              name="size"
              control={control}
              label="Select Size"
              placeholder="Select Value"
              options={gearSizeOptions}
            />
          )} */}

          {/* Color Dropdown */}
          {/* {selectedGearType && gearColorOptions.length > 0 && (
            <FormSelect
              name="color"
              control={control}
              label="Select Color"
              placeholder="Select Value"
              options={gearColorOptions}
            />
          )} */}

          {/* Submit Button */}
          <FormSubmitButton
            submitLabel="Request Gear"
            onSubmit={handleSubmit(onSubmit)}
            isSubmitting={isSubmitting}
            isValid={isValid}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  submitButton: {
    marginTop: 20,
  },
  flex: { flex: 1 },
});
